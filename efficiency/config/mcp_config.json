{"mcpServers": {"files": {"command": "echo", "args": ["MCP files service ready - this is a demo"], "enabled": true, "connection_type": "stdio", "on_demand": true}, "playwright": {"command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "enabled": true, "connection_type": "stdio", "on_demand": true}, "duckduckgo-remote": {"connection_type": "http", "server_name": "@nickclyde/duckduckgo-mcp-server", "api_key": "************************************", "profile_id": "", "config": {}, "enabled": true}, "chinarailway": {"connection_type": "http", "server_name": "@shenpeiheng/mcp-server-chinarailway", "api_key": "************************************", "profile_id": "", "config": {}, "enabled": true}, "hotnews": {"connection_type": "http", "server_name": "@wopal/mcp-server-hotnews", "api_key": "************************************", "profile_id": "", "config": {}, "enabled": true}}}