#!/usr/bin/env python3
"""
简单的文件系统MCP服务器
提供基本的文件和目录操作功能
"""
import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Any, Dict, List

# MCP协议消息处理
class SimpleMCPFileServer:
    """简单的MCP文件服务器"""
    
    def __init__(self):
        self.tools = [
            {
                "name": "list_directory",
                "description": "列出目录中的文件和子目录",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要列出的目录路径"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "read_file",
                "description": "读取文件内容",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "要读取的文件路径"
                        }
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "get_file_info",
                "description": "获取文件或目录信息",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "path": {
                            "type": "string",
                            "description": "文件或目录路径"
                        }
                    },
                    "required": ["path"]
                }
            }
        ]
    
    async def handle_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """处理MCP请求"""
        method = request.get("method")
        params = request.get("params", {})
        
        if method == "tools/list":
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "result": {
                    "tools": self.tools
                }
            }
        
        elif method == "tools/call":
            tool_name = params.get("name")
            arguments = params.get("arguments", {})
            
            try:
                if tool_name == "list_directory":
                    result = await self.list_directory(arguments.get("path", "."))
                elif tool_name == "read_file":
                    result = await self.read_file(arguments.get("path"))
                elif tool_name == "get_file_info":
                    result = await self.get_file_info(arguments.get("path"))
                else:
                    raise ValueError(f"未知工具: {tool_name}")
                
                return {
                    "jsonrpc": "2.0",
                    "id": request.get("id"),
                    "result": {
                        "content": [
                            {
                                "type": "text",
                                "text": result
                            }
                        ]
                    }
                }
            
            except Exception as e:
                return {
                    "jsonrpc": "2.0",
                    "id": request.get("id"),
                    "error": {
                        "code": -1,
                        "message": str(e)
                    }
                }
        
        elif method == "initialize":
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "result": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "serverInfo": {
                        "name": "simple-file-server",
                        "version": "1.0.0"
                    }
                }
            }
        
        else:
            return {
                "jsonrpc": "2.0",
                "id": request.get("id"),
                "error": {
                    "code": -32601,
                    "message": f"方法未找到: {method}"
                }
            }
    
    async def list_directory(self, path: str) -> str:
        """列出目录内容"""
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                return f"❌ 路径不存在: {path}"
            
            if not path_obj.is_dir():
                return f"❌ 不是目录: {path}"
            
            items = []
            for item in sorted(path_obj.iterdir()):
                if item.is_dir():
                    items.append(f"📁 {item.name}/")
                else:
                    size = item.stat().st_size
                    items.append(f"📄 {item.name} ({size} bytes)")
            
            if not items:
                return f"📂 目录为空: {path}"
            
            result = f"📂 目录内容: {path}\n"
            result += "\n".join(items)
            return result
            
        except Exception as e:
            return f"❌ 列出目录失败: {str(e)}"
    
    async def read_file(self, path: str) -> str:
        """读取文件内容"""
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                return f"❌ 文件不存在: {path}"
            
            if not path_obj.is_file():
                return f"❌ 不是文件: {path}"
            
            # 限制文件大小
            if path_obj.stat().st_size > 1024 * 1024:  # 1MB
                return f"❌ 文件太大: {path} (超过1MB)"
            
            with open(path_obj, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return f"📄 文件内容: {path}\n\n{content}"
            
        except UnicodeDecodeError:
            return f"❌ 无法读取文件（非文本文件）: {path}"
        except Exception as e:
            return f"❌ 读取文件失败: {str(e)}"
    
    async def get_file_info(self, path: str) -> str:
        """获取文件信息"""
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                return f"❌ 路径不存在: {path}"
            
            stat = path_obj.stat()
            
            info = f"📋 文件信息: {path}\n"
            info += f"类型: {'目录' if path_obj.is_dir() else '文件'}\n"
            info += f"大小: {stat.st_size} bytes\n"
            info += f"修改时间: {stat.st_mtime}\n"
            
            if path_obj.is_dir():
                try:
                    item_count = len(list(path_obj.iterdir()))
                    info += f"包含项目: {item_count} 个\n"
                except:
                    pass
            
            return info
            
        except Exception as e:
            return f"❌ 获取文件信息失败: {str(e)}"

async def main():
    """主函数 - 处理STDIO通信"""
    server = SimpleMCPFileServer()
    
    # 发送初始化完成通知
    sys.stderr.write("Simple MCP File Server started\n")
    sys.stderr.flush()
    
    while True:
        try:
            # 从stdin读取请求
            line = sys.stdin.readline()
            if not line:
                break
            
            line = line.strip()
            if not line:
                continue
            
            # 解析JSON请求
            try:
                request = json.loads(line)
            except json.JSONDecodeError:
                continue
            
            # 处理请求
            response = await server.handle_request(request)
            
            # 发送响应到stdout
            print(json.dumps(response))
            sys.stdout.flush()
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            sys.stderr.write(f"Error: {e}\n")
            sys.stderr.flush()

if __name__ == "__main__":
    asyncio.run(main())
