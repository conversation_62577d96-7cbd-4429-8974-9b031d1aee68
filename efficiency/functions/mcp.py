"""
MCP功能统一入口 - 提供所有MCP相关功能的简化接口
"""
import logging
from typing import Dict, Any, List, Tuple, Optional

# 导入统一的MCP实现
from .universal_mcp import universal_mcp as unified_mcp

# 配置日志
logger = logging.getLogger(__name__)

#
# 服务器管理接口
#
def get_all_servers() -> Dict[str, Dict[str, Any]]:
    """获取所有MCP服务器配置"""
    return unified_mcp.get_all_servers()

def get_server_config(server_name: str) -> Optional[Dict[str, Any]]:
    """获取指定服务器的配置"""
    return unified_mcp.get_server_config(server_name)

def update_server_config(server_name: str, config: Dict[str, Any]) -> None:
    """更新服务器配置"""
    unified_mcp.update_server_config(server_name, config)

def remove_server_config(server_name: str) -> None:
    """删除服务器配置"""
    unified_mcp.remove_server_config(server_name)

def toggle_server_status(server_name: str, enabled: bool) -> None:
    """切换服务器状态"""
    unified_mcp.toggle_server_status(server_name, enabled)

def start_server(server_name: str) -> bool:
    """启动MCP服务器"""
    return unified_mcp.start_server(server_name)

def stop_server(server_name: str) -> bool:
    """停止MCP服务器"""
    return unified_mcp.stop_server(server_name)

def start_all_enabled_servers() -> Dict[str, bool]:
    """启动所有启用的MCP服务器"""
    return unified_mcp.start_all_enabled_servers()

def stop_all_servers() -> Dict[str, bool]:
    """停止所有MCP服务器"""
    return unified_mcp.stop_all_servers()

def get_server_status(server_name: str) -> str:
    """获取服务器状态"""
    return unified_mcp.get_server_status(server_name)

def get_all_server_status() -> Dict[str, str]:
    """获取所有服务器状态"""
    return unified_mcp.get_all_server_status()

def get_server_logs(server_name: str) -> Dict[str, Any]:
    """获取服务器日志"""
    return unified_mcp.get_server_logs(server_name)

def get_all_server_logs() -> Dict[str, Dict[str, Any]]:
    """获取所有服务器日志"""
    return unified_mcp.get_all_server_logs()

def clear_server_logs(server_name: str) -> bool:
    """清除服务器日志"""
    return unified_mcp.clear_server_logs(server_name)

#
# 工具调用接口
#
def use_mcp_tool(server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Tuple[bool, str]:
    """使用MCP工具

    Args:
        server_name: 服务器名称
        tool_name: 工具名称
        arguments: 工具参数

    Returns:
        (成功标志, 结果)
    """
    return unified_mcp.use_mcp_tool(server_name, tool_name, arguments)

def access_mcp_resource(server_name: str, uri: str) -> Tuple[bool, str]:
    """访问MCP资源

    Args:
        server_name: 服务器名称
        uri: 资源URI

    Returns:
        (成功标志, 结果)
    """
    return unified_mcp.access_mcp_resource(server_name, uri)

#
# 便捷工具函数
#
def query_trains(from_city: str, to_city: str, date: str = "tomorrow") -> str:
    """
    查询火车票的便捷函数

    Args:
        from_city: 出发城市
        to_city: 到达城市
        date: 日期（默认明天）

    Returns:
        查询结果
    """
    arguments = {
        "fromCity": from_city,
        "toCity": to_city,
        "date": date
    }

    success, result = use_mcp_tool("chinarailway", "search", arguments)

    if success:
        return f"🚄 **火车票查询结果: {from_city} → {to_city} ({date})**\n\n{result}"
    else:
        return f"❌ **火车票查询失败: {from_city} → {to_city} ({date})**\n\n{result}"

def read_file(path: str) -> str:
    """
    读取文件内容的便捷函数

    Args:
        path: 文件路径

    Returns:
        文件内容
    """
    success, result = use_mcp_tool("files", "read_file", {"path": path})

    if success:
        return result
    else:
        return f"❌ 读取文件失败: {path}\n\n{result}"

def write_file(path: str, content: str) -> str:
    """
    写入文件内容的便捷函数

    Args:
        path: 文件路径
        content: 文件内容

    Returns:
        操作结果
    """
    success, result = use_mcp_tool("files", "write_file", {"path": path, "content": content})

    if success:
        return f"✅ 文件写入成功: {path}"
    else:
        return f"❌ 文件写入失败: {path}\n\n{result}"

def list_directory(path: str = ".", recursive: bool = False) -> str:
    """
    列出目录内容的便捷函数

    Args:
        path: 目录路径
        recursive: 是否递归列出子目录

    Returns:
        目录内容
    """
    success, result = use_mcp_tool("files", "list_directory", {"path": path, "recursive": recursive})

    if success:
        return result
    else:
        return f"❌ 列出目录失败: {path}\n\n{result}"
