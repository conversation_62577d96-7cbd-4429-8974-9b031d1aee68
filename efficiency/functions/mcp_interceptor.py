#!/usr/bin/env python3
"""
MCP智能拦截器 - 拦截并修正所有MCP调用
确保模型的任何MCP调用都能正确执行
"""
import logging
import json
from typing import Dict, Any, Tuple, Optional

logger = logging.getLogger(__name__)

class MCPInterceptor:
    """MCP智能拦截器 - 拦截并自动修正所有MCP调用"""

    def __init__(self):
        # 扩展的服务知识库 - 支持所有MCP服务
        self.service_knowledge = {
            'hotnews': {
                'keywords': ['热榜', '热搜', '热点', '新闻', '微博', '知乎', '百度', 'b站', '抖音'],
                'tools': {
                    'get_hot_news': {
                        'required_params': ['sources'],
                        'param_types': {
                            'sources': 'array_of_integers'
                        },
                        'platform_mapping': {
                            'zhihu': 1, '知乎': 1,
                            '36kr': 2, '36氪': 2,
                            'baidu': 3, '百度': 3,
                            'bilibili': 4, 'b站': 4, '哔哩哔哩': 4,
                            'weibo': 5, '微博': 5,
                            'douyin': 6, '抖音': 6,
                            'hupu': 7, '虎扑': 7,
                            'douban': 8, '豆瓣': 8,
                            'it': 9, 'it新闻': 9
                        },
                        'default_sources': [1]  # 默认知乎
                    }
                },
                'tool_aliases': {
                    'get_news': 'get_hot_news',
                    'get_zhihu_news': 'get_hot_news',
                    'get_weibo_news': 'get_hot_news',
                    'list_sources': 'get_hot_news',
                    'get_hot_list': 'get_hot_news'
                }
            },
            'duckduckgo-remote': {
                'keywords': ['搜索', 'search', '查找', '查询'],
                'tools': {
                    'search': {
                        'required_params': ['query'],
                        'param_types': {
                            'query': 'string',
                            'limit': 'integer'
                        }
                    }
                },
                'tool_aliases': {
                    'query': 'search',
                    'find': 'search'
                }
            },
            'chinarailway': {
                'keywords': ['火车', '高铁', '铁路', '车票', '列车', '动车'],
                'tools': {
                    'search': {
                        'required_params': ['fromCity', 'toCity', 'date'],
                        'param_types': {
                            'fromCity': 'string',
                            'toCity': 'string',
                            'date': 'string'
                        }
                    }
                },
                'tool_aliases': {
                    'query_trains': 'search',
                    'search_trains': 'search',
                    'find_trains': 'search',
                    'get_trains': 'search'
                }
            },
            'files': {
                'keywords': ['文件', '目录', '读取', '写入', 'file', 'directory', 'read', 'write'],
                'tools': {
                    'list_directory': {
                        'required_params': ['path'],
                        'param_types': {
                            'path': 'string'
                        }
                    },
                    'read_file': {
                        'required_params': ['path'],
                        'param_types': {
                            'path': 'string'
                        }
                    }
                },
                'tool_aliases': {
                    'list': 'list_directory',
                    'ls': 'list_directory',
                    'read': 'read_file',
                    'cat': 'read_file'
                }
            },
            'playwright': {
                'keywords': ['网页', '浏览器', '截图', '导航', 'web', 'browser', 'screenshot', 'navigate', 'playwright', '打开', '访问'],
                'tools': {
                    'playwright_navigate': {
                        'required_params': ['url'],
                        'param_types': {
                            'url': 'string'
                        }
                    },
                    'navigate': {
                        'required_params': ['url'],
                        'param_types': {
                            'url': 'string'
                        }
                    },
                    'screenshot': {
                        'required_params': ['url'],
                        'param_types': {
                            'url': 'string'
                        }
                    },
                    'goto': {
                        'required_params': ['url'],
                        'param_types': {
                            'url': 'string'
                        }
                    }
                },
                'tool_aliases': {
                    'capture': 'screenshot',
                    'snap': 'screenshot',
                    'open': 'navigate',
                    '打开': 'navigate',
                    '访问': 'navigate',
                    '导航': 'navigate',
                    'go': 'navigate',
                    'visit': 'navigate'
                }
            }
        }

    def intercept_and_fix(self, server_name: str, tool_name: str, arguments: Dict[str, Any],
                         user_context: str = "") -> Tuple[str, str, Dict[str, Any]]:
        """
        拦截并修正MCP调用

        Args:
            server_name: 服务名
            tool_name: 工具名
            arguments: 参数
            user_context: 用户上下文（用于智能推断）

        Returns:
            (修正后的服务名, 修正后的工具名, 修正后的参数)
        """
        try:
            logger.info(f"拦截MCP调用: {server_name}.{tool_name}({arguments})")

            # 🚀 智能服务选择 - 如果服务名不合适，自动选择更好的服务
            best_server = self._intelligent_service_selection(server_name, tool_name, arguments, user_context)

            # 1. 修正服务名
            fixed_server = self._fix_server_name(best_server)

            # 2. 修正工具名
            fixed_tool = self._fix_tool_name(fixed_server, tool_name)

            # 3. 修正参数
            fixed_args = self._fix_arguments(fixed_server, fixed_tool, arguments, user_context)

            logger.info(f"修正后调用: {fixed_server}.{fixed_tool}({fixed_args})")

            return fixed_server, fixed_tool, fixed_args

        except Exception as e:
            logger.error(f"MCP拦截修正失败: {e}")
            return server_name, tool_name, arguments

    def _intelligent_service_selection(self, server_name: str, tool_name: str,
                                     arguments: Dict[str, Any], user_context: str) -> str:
        """智能服务选择 - 根据上下文自动选择最合适的服务"""

        # 如果指定的服务在知识库中且合理，直接使用
        if server_name in self.service_knowledge:
            return server_name

        # 分析用户上下文，智能选择服务
        context_lower = user_context.lower()

        # 计算每个服务的匹配分数
        service_scores = {}

        for service_name, service_info in self.service_knowledge.items():
            score = 0
            keywords = service_info.get('keywords', [])

            # 关键词匹配
            for keyword in keywords:
                if keyword in context_lower:
                    score += 10

            # 工具名匹配
            if tool_name in service_info.get('tool_aliases', {}):
                score += 5

            if tool_name in service_info.get('tools', {}):
                score += 8

            # 参数匹配
            for arg_key in arguments.keys():
                for tool_info in service_info.get('tools', {}).values():
                    if arg_key in tool_info.get('required_params', []):
                        score += 3

            service_scores[service_name] = score

        # 选择得分最高的服务
        if service_scores:
            best_service = max(service_scores, key=service_scores.get)
            best_score = service_scores[best_service]

            # 如果得分足够高，使用推荐的服务
            if best_score > 5:
                logger.info(f"智能选择服务: {server_name} → {best_service} (得分: {best_score})")
                return best_service

        # 如果没有好的匹配，使用原始服务名
        return server_name

    def _fix_server_name(self, server_name: str) -> str:
        """修正服务名"""
        # 服务名别名映射
        server_aliases = {
            'news': 'hotnews',
            'hot': 'hotnews',
            'search': 'duckduckgo-remote',
            'duckduckgo': 'duckduckgo-remote'
        }

        return server_aliases.get(server_name.lower(), server_name)

    def _fix_tool_name(self, server_name: str, tool_name: str) -> str:
        """修正工具名"""
        if server_name not in self.service_knowledge:
            return tool_name

        aliases = self.service_knowledge[server_name].get('tool_aliases', {})
        return aliases.get(tool_name, tool_name)

    def _fix_arguments(self, server_name: str, tool_name: str, arguments: Dict[str, Any],
                      user_context: str) -> Dict[str, Any]:
        """修正参数"""
        if server_name not in self.service_knowledge:
            return arguments

        service_info = self.service_knowledge[server_name]
        tool_info = service_info.get('tools', {}).get(tool_name, {})

        if not tool_info:
            return arguments

        fixed_args = arguments.copy()

        # 特殊处理hotnews服务
        if server_name == 'hotnews' and tool_name == 'get_hot_news':
            fixed_args = self._fix_hotnews_args(fixed_args, tool_info, user_context)

        # 特殊处理搜索服务
        elif server_name == 'duckduckgo-remote' and tool_name == 'search':
            fixed_args = self._fix_search_args(fixed_args, user_context)

        # 特殊处理火车票服务
        elif server_name == 'chinarailway' and tool_name == 'search':
            fixed_args = self._fix_railway_args(fixed_args, user_context)

        # 特殊处理文件服务
        elif server_name == 'files':
            fixed_args = self._fix_files_args(fixed_args, tool_name, user_context)

        # 特殊处理浏览器服务
        elif server_name == 'playwright':
            fixed_args = self._fix_playwright_args(fixed_args, user_context)

        # 确保必需参数存在
        required_params = tool_info.get('required_params', [])
        for param in required_params:
            if param not in fixed_args:
                fixed_args[param] = self._get_default_value(server_name, tool_name, param, user_context)

        return fixed_args

    def _fix_hotnews_args(self, args: Dict[str, Any], tool_info: Dict[str, Any],
                         user_context: str) -> Dict[str, Any]:
        """修正hotnews参数"""
        fixed = args.copy()
        platform_mapping = tool_info.get('platform_mapping', {})

        # 处理sources参数
        if 'sources' not in fixed or not fixed['sources']:
            # 从用户上下文推断平台
            detected_sources = []
            context_lower = user_context.lower()

            for platform_name, platform_id in platform_mapping.items():
                if platform_name in context_lower:
                    detected_sources.append(platform_id)

            if not detected_sources:
                detected_sources = tool_info.get('default_sources', [1])

            fixed['sources'] = detected_sources

        # 修正错误的sources格式
        elif isinstance(fixed['sources'], list):
            corrected_sources = []
            for source in fixed['sources']:
                if isinstance(source, str):
                    # 字符串转ID
                    source_id = platform_mapping.get(source.lower())
                    if source_id:
                        corrected_sources.append(source_id)
                elif isinstance(source, int):
                    corrected_sources.append(source)

            if corrected_sources:
                fixed['sources'] = corrected_sources
            else:
                fixed['sources'] = tool_info.get('default_sources', [1])

        # 移除无效参数
        invalid_params = ['count', 'platform', 'source']
        for param in invalid_params:
            fixed.pop(param, None)

        return fixed

    def _fix_search_args(self, args: Dict[str, Any], user_context: str) -> Dict[str, Any]:
        """修正搜索参数"""
        fixed = args.copy()

        # 确保有query参数
        if 'query' not in fixed and user_context:
            # 从上下文提取查询词
            keywords = []
            for word in user_context.split():
                if word not in ['搜索', '查询', '查找', '最新', '条', '个']:
                    keywords.append(word)
            fixed['query'] = ' '.join(keywords) if keywords else user_context

        # 处理limit参数
        if 'count' in fixed:
            fixed['limit'] = fixed.pop('count')

        return fixed

    def _fix_railway_args(self, args: Dict[str, Any], user_context: str) -> Dict[str, Any]:
        """修正火车票查询参数"""
        fixed = args.copy()

        # 处理城市名称
        if 'from' in fixed:
            fixed['fromCity'] = fixed.pop('from')
        if 'to' in fixed:
            fixed['toCity'] = fixed.pop('to')

        # 处理日期
        if 'date' not in fixed and user_context:
            if '明天' in user_context:
                from datetime import datetime, timedelta
                fixed['date'] = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
            elif '今天' in user_context:
                from datetime import datetime
                fixed['date'] = datetime.now().strftime("%Y-%m-%d")

        return fixed

    def _fix_files_args(self, args: Dict[str, Any], tool_name: str, user_context: str) -> Dict[str, Any]:
        """修正文件服务参数"""
        fixed = args.copy()

        # 确保有path参数
        if 'path' not in fixed:
            if user_context:
                # 从上下文提取路径
                import re
                path_match = re.search(r'[/\\][\w/\\.-]+', user_context)
                if path_match:
                    fixed['path'] = path_match.group()
                else:
                    fixed['path'] = '.'  # 默认当前目录
            else:
                fixed['path'] = '.'

        return fixed

    def _fix_playwright_args(self, args: Dict[str, Any], user_context: str) -> Dict[str, Any]:
        """修正浏览器自动化参数"""
        fixed = args.copy()

        # 确保有url参数
        if 'url' not in fixed and user_context:
            # 从上下文提取URL
            import re
            url_match = re.search(r'https?://[^\s]+', user_context)
            if url_match:
                fixed['url'] = url_match.group()
            elif 'example' in user_context.lower():
                fixed['url'] = 'https://example.com'

        return fixed

    def _get_default_value(self, server_name: str, tool_name: str, param_name: str,
                          user_context: str) -> Any:
        """获取参数默认值"""
        if server_name == 'hotnews' and param_name == 'sources':
            return [1]  # 默认知乎
        elif param_name == 'query':
            return user_context or "热门"
        elif param_name in ['limit', 'count']:
            # 从上下文提取数字
            import re
            numbers = re.findall(r'\d+', user_context)
            return int(numbers[0]) if numbers else 10
        elif param_name == 'path':
            return '.'  # 默认当前目录
        elif param_name == 'url':
            return 'https://example.com'  # 默认示例URL
        elif param_name in ['fromCity', 'toCity']:
            # 从上下文提取城市名
            cities = ['北京', '上海', '广州', '深圳']
            for city in cities:
                if city in user_context:
                    return city
            return '北京'  # 默认北京
        elif param_name == 'date':
            from datetime import datetime
            return datetime.now().strftime("%Y-%m-%d")  # 默认今天

        return None

# 创建全局拦截器实例
mcp_interceptor = MCPInterceptor()
