#!/usr/bin/env python3
"""
智能MCP包装器 - 提供通用的MCP调用接口
"""
import logging
from typing import Dict, Any, List, Tuple

logger = logging.getLogger(__name__)

def smart_mcp_call(user_intent: str, suggested_server: str = None,
                  suggested_tool: str = None, suggested_args: Dict[str, Any] = None) -> Tu<PERSON>[bool, str]:
    """
    智能MCP调用 - 根据用户意图自动选择服务和参数

    Args:
        user_intent: 用户意图描述
        suggested_server: 建议的服务名（可选）
        suggested_tool: 建议的工具名（可选）
        suggested_args: 建议的参数（可选）

    Returns:
        (是否成功, 结果内容或错误信息)
    """
    try:
        from .universal_mcp import universal_mcp

        # 1. 如果有明确的建议，先尝试修正后调用
        if suggested_server and suggested_tool:
            logger.info(f"尝试建议的服务: {suggested_server}.{suggested_tool}")

            # 智能修正参数和工具名
            corrected_tool, corrected_args = _smart_correct_call(
                suggested_server, suggested_tool, suggested_args or {}, user_intent
            )

            success, result = universal_mcp.use_mcp_tool(suggested_server, corrected_tool, corrected_args)
            if success:
                return True, result
            else:
                logger.warning(f"建议的调用失败: {result}")

        # 2. 自动选择和尝试合适的服务
        return _intelligent_service_selection(user_intent, suggested_args or {})

    except Exception as e:
        logger.error(f"智能MCP调用失败: {e}")
        return False, f"智能调用失败: {str(e)}"

def _smart_correct_call(server_name: str, tool_name: str, args: Dict[str, Any],
                       user_intent: str) -> Tuple[str, Dict[str, Any]]:
    """智能修正调用参数"""

    # 修正工具名称
    corrected_tool = _correct_tool_name(server_name, tool_name)

    # 修正参数
    corrected_args = _correct_arguments(server_name, corrected_tool, args, user_intent)

    return corrected_tool, corrected_args

def _correct_tool_name(server_name: str, tool_name: str) -> str:
    """修正工具名称"""

    # 常见的工具名称映射
    tool_mappings = {
        'hotnews': {
            'get_news': 'get_hot_news',
            'news': 'get_hot_news',
            'hot_news': 'get_hot_news',
            'hotlist': 'get_hot_news',
        },
        'duckduckgo-remote': {
            'query': 'search',
            'find': 'search',
        }
    }

    server_mappings = tool_mappings.get(server_name, {})
    return server_mappings.get(tool_name, tool_name)

def _correct_arguments(server_name: str, tool_name: str, args: Dict[str, Any],
                      user_intent: str) -> Dict[str, Any]:
    """智能修正参数"""

    corrected = args.copy()
    intent_lower = user_intent.lower()

    # 针对不同服务的参数修正
    if server_name == 'hotnews':
        corrected = _correct_hotnews_args(corrected, intent_lower)
    elif server_name == 'duckduckgo-remote':
        corrected = _correct_search_args(corrected, intent_lower)

    return corrected

def _correct_hotnews_args(args: Dict[str, Any], intent: str) -> Dict[str, Any]:
    """修正HotNews参数"""
    corrected = args.copy()

    # 平台映射
    platform_mappings = {
        '知乎': [1], '微博': [5], '百度': [3], 'b站': [4], '哔哩哔哩': [4],
        '抖音': [6], '豆瓣': [8], '虎扑': [7], '36氪': [2], 'it': [9]
    }

    # 处理source/platform参数
    if 'source' in corrected:
        source_value = corrected.pop('source')
        if isinstance(source_value, str):
            for platform, source_ids in platform_mappings.items():
                if platform in source_value or source_value in platform:
                    corrected['sources'] = source_ids
                    break

    if 'platform' in corrected:
        platform_value = corrected.pop('platform')
        if isinstance(platform_value, str):
            for platform, source_ids in platform_mappings.items():
                if platform in platform_value or platform_value in platform:
                    corrected['sources'] = source_ids
                    break

    # 从用户意图中自动检测平台
    if 'sources' not in corrected:
        detected_platforms = []
        for platform, source_ids in platform_mappings.items():
            if platform in intent:
                detected_platforms.extend(source_ids)

        if detected_platforms:
            corrected['sources'] = detected_platforms
        else:
            # 默认使用知乎
            corrected['sources'] = [1]

    # 移除无用参数
    corrected.pop('count', None)

    return corrected

def _correct_search_args(args: Dict[str, Any], intent: str) -> Dict[str, Any]:
    """修正搜索参数"""
    corrected = args.copy()

    # 如果没有query，从意图中生成
    if 'query' not in corrected:
        if '微博' in intent:
            corrected['query'] = 'site:weibo.com 最新'
        elif '知乎' in intent:
            corrected['query'] = 'site:zhihu.com'
        else:
            # 提取关键词
            keywords = []
            for word in intent.split():
                if word not in ['查询', '搜索', '最新', '条']:
                    keywords.append(word)
            corrected['query'] = ' '.join(keywords) if keywords else intent

    # 参数名映射
    if 'count' in corrected:
        corrected['limit'] = corrected.pop('count')

    return corrected

def _intelligent_service_selection(user_intent: str, base_args: Dict[str, Any]) -> Tuple[bool, str]:
    """智能服务选择"""

    try:
        from .universal_mcp import universal_mcp

        intent_lower = user_intent.lower()

        # 服务优先级列表
        service_priorities = []

        # 根据意图确定服务优先级
        if any(keyword in intent_lower for keyword in ['热榜', '热搜', '热点', '新闻', '微博', '知乎', '百度', 'b站', '抖音']):
            service_priorities.append(('hotnews', 'get_hot_news'))

        if any(keyword in intent_lower for keyword in ['搜索', 'search', '查找', '查询']):
            service_priorities.append(('duckduckgo-remote', 'search'))

        if any(keyword in intent_lower for keyword in ['文件', '目录', '读取', '写入', 'file', 'directory', 'read', 'write']):
            service_priorities.append(('files', 'list_directory'))

        if any(keyword in intent_lower for keyword in ['网页', '浏览器', '截图', 'web', 'browser', 'screenshot', 'playwright']):
            service_priorities.append(('playwright', 'screenshot'))

        # 如果没有明确匹配，尝试所有可用服务
        if not service_priorities:
            service_priorities = [
                ('hotnews', 'get_hot_news'),
                ('duckduckgo-remote', 'search'),
                ('files', 'list_directory'),
                ('playwright', 'screenshot')
            ]

        # 尝试每个服务
        for server_name, tool_name in service_priorities:
            logger.info(f"尝试服务: {server_name}.{tool_name}")

            # 生成适合的参数
            adapted_args = _correct_arguments(server_name, tool_name, base_args, user_intent)

            logger.info(f"使用参数: {adapted_args}")

            success, result = universal_mcp.use_mcp_tool(server_name, tool_name, adapted_args)

            if success:
                logger.info(f"✅ 成功调用: {server_name}.{tool_name}")
                return True, result
            else:
                logger.warning(f"失败: {result}")

        return False, "未找到合适的MCP服务来处理此请求"

    except Exception as e:
        logger.error(f"智能服务选择失败: {e}")
        return False, f"智能服务选择失败: {str(e)}"
