#!/usr/bin/env python3
"""
通用MCP工具调用接口
"""
import asyncio
import json
import base64
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

try:
    from .mcp_server import MCPServerManager
except ImportError:
    from mcp_server import MCPServerManager

# 设置日志
logger = logging.getLogger(__name__)

class UniversalMCPInterface:
    """通用MCP接口"""

    def __init__(self):
        self.manager = MCPServerManager()

    def use_mcp_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> tuple[bool, str]:
        """
        同步版本的MCP工具调用接口

        Args:
            server_name: MCP服务名称
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            (是否成功, 结果内容或错误信息)
        """
        try:
            # 🚀 智能拦截和修正 - 在任何处理之前先修正调用
            try:
                from .mcp_interceptor import mcp_interceptor

                logger.info(f"原始MCP调用: {server_name}.{tool_name}({arguments})")

                # 拦截并修正调用
                fixed_server, fixed_tool, fixed_args = mcp_interceptor.intercept_and_fix(
                    server_name, tool_name, arguments, ""
                )

                logger.info(f"修正后调用: {fixed_server}.{fixed_tool}({fixed_args})")

                # 使用修正后的参数
                server_name, tool_name, arguments = fixed_server, fixed_tool, fixed_args

            except Exception as e:
                logger.warning(f"MCP拦截器失败，使用原始参数: {e}")

            # 添加调试日志
            logger.info(f"开始MCP工具调用: {server_name}.{tool_name}, 参数: {arguments}")

            # 运行异步调用
            result = asyncio.run(self._async_use_mcp_tool(server_name, tool_name, arguments))

            logger.info(f"MCP工具调用结果: {result}")

            if result["success"]:
                return True, result["content"]
            else:
                return False, result["error"]

        except Exception as e:
            logger.error(f"MCP工具调用失败: {server_name}.{tool_name}, 错误: {e}")
            return False, f"调用失败: {str(e)}"

    async def _async_use_mcp_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """异步版本的MCP工具调用"""
        try:
            # 获取服务配置
            server_config = self.manager.get_server_config(server_name)
            logger.info(f"获取服务配置: {server_name}, 配置: {server_config}")

            if not server_config:
                return {
                    "success": False,
                    "error": f"未找到服务 {server_name} 的配置"
                }

            enabled_status = server_config.get("enabled", False)
            logger.info(f"服务 {server_name} 启用状态: {enabled_status}")

            if not enabled_status:
                return {
                    "success": False,
                    "error": f"服务 {server_name} 未启用"
                }

            # 检查服务状态
            status = self.manager.get_server_status(server_name)
            logger.info(f"服务 {server_name} 运行状态: {status}")

            # 对于按需启动的服务，ready状态也是可以调用的
            if status not in ['running', 'ready']:
                return {
                    "success": False,
                    "error": f"服务 {server_name} 不可用，当前状态: {status}"
                }

            # 预处理工具名称和参数
            corrected_tool_name = self._correct_tool_name(server_name, tool_name)
            processed_arguments = self._preprocess_arguments(arguments)

            # 智能重试机制
            return await self._call_with_intelligent_retry(
                server_config, server_name, corrected_tool_name, processed_arguments
            )

        except Exception as e:
            logger.error(f"MCP工具调用失败: {server_name}.{tool_name}, 错误: {e}")
            return {
                "success": False,
                "error": f"调用失败: {str(e)}"
            }

    async def _call_with_intelligent_retry(self, server_config: Dict[str, Any],
                                         server_name: str, tool_name: str,
                                         arguments: Dict[str, Any]) -> Dict[str, Any]:
        """带智能重试的MCP工具调用"""
        connection_type = server_config.get("connection_type", "stdio")

        # 首次尝试原始参数
        logger.info(f"首次尝试调用 {server_name}.{tool_name}，参数: {arguments}")

        if connection_type == "http":
            result = await self._call_http_tool(server_config, tool_name, arguments)
        else:
            result = await self._call_stdio_tool(server_name, tool_name, arguments)

        # 如果首次调用成功，直接返回
        if result.get("success"):
            logger.info(f"首次调用成功: {server_name}.{tool_name}")
            return result

        # 首次调用失败，开始智能重试
        error_message = result.get("error", "")
        logger.warning(f"首次调用失败: {error_message}")

        # 获取工具schema（如果可能）
        tool_schema = await self._get_tool_schema(server_config, tool_name)

        # 生成参数调整建议
        suggestions = self._intelligent_parameter_adjustment(
            server_name, tool_name, arguments, error_message, tool_schema
        )

        logger.info(f"生成了 {len(suggestions)} 个参数调整建议")

        # 尝试每个建议
        for i, suggested_args in enumerate(suggestions):
            logger.info(f"尝试建议 {i+1}/{len(suggestions)}: {suggested_args}")

            if connection_type == "http":
                retry_result = await self._call_http_tool(server_config, tool_name, suggested_args)
            else:
                retry_result = await self._call_stdio_tool(server_name, tool_name, suggested_args)

            if retry_result.get("success"):
                logger.info(f"重试成功! 使用参数: {suggested_args}")
                # 可以在这里记录成功的参数模式，用于未来的学习
                return retry_result
            else:
                logger.warning(f"建议 {i+1} 失败: {retry_result.get('error', '')}")

        # 所有重试都失败，返回最后的错误
        logger.error(f"所有重试都失败，返回原始错误")
        return result

    async def _call_http_tool(self, server_config: Dict[str, Any], tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用HTTP类型的MCP工具"""
        try:
            import mcp
            from mcp.client.streamable_http import streamablehttp_client

            # 预处理参数
            processed_args = self._preprocess_arguments(arguments)

            # 构建连接URL
            config = server_config.get('config', {})
            config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
            api_key = server_config.get('api_key', '')
            server_name = server_config.get('server_name', '')

            url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"

            # 连接并调用
            async with streamablehttp_client(url) as (read_stream, write_stream, _):
                async with mcp.ClientSession(read_stream, write_stream) as session:
                    await session.initialize()

                    # 调用工具
                    result = await session.call_tool(tool_name, processed_args)

                    # 解析结果
                    return self._parse_result(result)

        except ImportError:
            return {
                "success": False,
                "error": "MCP SDK未安装，请运行: pip install mcp"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"HTTP调用失败: {str(e)}"
            }

    async def _call_stdio_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用STDIO类型的MCP工具"""
        try:
            # 使用现有的mcp_manager来调用STDIO工具
            from .mcp_client import mcp_manager

            # 确保服务已连接
            if not await mcp_manager.connect_to_server(server_name):
                return {
                    "success": False,
                    "error": f"无法连接到STDIO服务器 {server_name}"
                }

            # 调用工具
            result = await mcp_manager.call_tool(server_name, tool_name, **arguments)

            if result is None:
                return {
                    "success": False,
                    "error": "STDIO工具调用返回空结果"
                }

            # 解析结果
            return self._parse_stdio_result(result)

        except Exception as e:
            logger.error(f"STDIO工具调用失败: {server_name}.{tool_name}, 错误: {e}")
            return {
                "success": False,
                "error": f"STDIO调用失败: {str(e)}"
            }

    def _parse_stdio_result(self, result) -> Dict[str, Any]:
        """解析STDIO调用结果"""
        try:
            # 如果结果有content属性（MCP标准格式）
            if hasattr(result, 'content'):
                content = result.content
                if isinstance(content, list) and len(content) > 0:
                    # 提取文本内容
                    text_content = []
                    for item in content:
                        if hasattr(item, 'text'):
                            text_content.append(item.text)
                        else:
                            text_content.append(str(item))

                    return {
                        "success": True,
                        "content": "\n".join(text_content),
                        "raw_content": content
                    }
                else:
                    return {
                        "success": True,
                        "content": str(content),
                        "raw_content": content
                    }
            else:
                # 直接返回字符串结果
                return {
                    "success": True,
                    "content": str(result),
                    "raw_content": result
                }
        except Exception as e:
            logger.error(f"解析STDIO结果失败: {e}")
            return {
                "success": False,
                "error": f"解析结果失败: {str(e)}"
            }

    def _correct_tool_name(self, server_name: str, tool_name: str) -> str:
        """修正工具名称，处理常见的错误映射"""
        # 针对不同服务的工具名称映射
        tool_mappings = {
            'chinarailway': {
                'query_trains': 'search',
                'search_trains': 'search',
                'find_trains': 'search',
                'get_trains': 'search',
            },
            'files': {
                'list': 'list_directory',
                'ls': 'list_directory',
                'read': 'read_file',
                'cat': 'read_file',
                'write': 'write_file',
            }
        }

        # 获取服务特定的映射
        server_mappings = tool_mappings.get(server_name, {})

        # 返回映射后的工具名称，如果没有映射则返回原名称
        return server_mappings.get(tool_name, tool_name)

    def _preprocess_arguments(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """预处理参数，处理常见的格式转换"""
        processed = arguments.copy()

        # 处理常见的参数名映射
        param_mappings = {
            'from': 'fromCity',
            'to': 'toCity',
            '明天': 'tomorrow',
            '今天': 'today',
            '昨天': 'yesterday'
        }

        # 应用参数名映射
        for old_key, new_key in param_mappings.items():
            if old_key in processed:
                processed[new_key] = processed.pop(old_key)

        # 处理日期格式
        for key, value in processed.items():
            if isinstance(value, str):
                # 处理相对日期
                if value.lower() in ['today', '今天']:
                    processed[key] = datetime.now().strftime("%Y-%m-%d")
                elif value.lower() in ['tomorrow', '明天']:
                    processed[key] = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
                elif value.lower() in ['yesterday', '昨天']:
                    processed[key] = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

        return processed

    def _intelligent_parameter_adjustment(self, server_name: str, tool_name: str,
                                         original_args: Dict[str, Any], error_message: str,
                                         tool_schema: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """智能参数调整：根据错误信息和工具schema生成可能的参数组合"""
        suggestions = []

        # 分析错误信息，提取关键信息
        error_lower = error_message.lower()

        # 常见错误模式和对应的参数调整策略
        if "source" in error_lower and "id" in error_lower:
            # 错误提示需要source IDs
            suggestions.extend(self._generate_source_id_suggestions(original_args))

        if "platform" in error_lower:
            # 错误提示平台相关
            suggestions.extend(self._generate_platform_suggestions(original_args))

        if "parameter" in error_lower or "argument" in error_lower:
            # 通用参数错误
            suggestions.extend(self._generate_generic_parameter_suggestions(original_args, tool_schema))

        # 如果有工具schema，根据schema生成建议
        if tool_schema:
            suggestions.extend(self._generate_schema_based_suggestions(original_args, tool_schema))

        # 去重并限制建议数量
        unique_suggestions = []
        for suggestion in suggestions:
            if suggestion not in unique_suggestions:
                unique_suggestions.append(suggestion)

        return unique_suggestions[:5]  # 最多尝试5种参数组合

    def _generate_source_id_suggestions(self, original_args: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成source ID相关的参数建议"""
        suggestions = []

        # 常见的平台名称到ID映射（通用模式）
        platform_mappings = {
            '知乎': [1], 'zhihu': [1],
            '微博': [5], 'weibo': [5],
            '百度': [3], 'baidu': [3],
            'b站': [4], 'bilibili': [4],
            '抖音': [6], 'douyin': [6],
        }

        # 如果有platform参数，尝试转换为sources
        if 'platform' in original_args:
            platform = str(original_args['platform']).lower()
            for key, source_ids in platform_mappings.items():
                if key in platform or platform in key:
                    new_args = original_args.copy()
                    new_args.pop('platform', None)
                    new_args['sources'] = source_ids
                    suggestions.append(new_args)

        # 如果没有sources参数，添加默认的sources
        if 'sources' not in original_args:
            # 尝试常见的source ID组合
            common_sources = [[1], [1, 5], [1, 3, 5], list(range(1, 10))]
            for sources in common_sources:
                new_args = original_args.copy()
                new_args['sources'] = sources
                suggestions.append(new_args)

        return suggestions

    def _generate_platform_suggestions(self, original_args: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成平台相关的参数建议"""
        suggestions = []

        # 尝试不同的平台参数名称
        platform_param_names = ['platform', 'platforms', 'source', 'sources', 'site', 'sites']

        if 'platform' in original_args:
            platform_value = original_args['platform']
            for param_name in platform_param_names:
                if param_name != 'platform':
                    new_args = original_args.copy()
                    new_args.pop('platform', None)
                    if param_name.endswith('s'):  # 复数形式，使用数组
                        new_args[param_name] = [platform_value] if not isinstance(platform_value, list) else platform_value
                    else:
                        new_args[param_name] = platform_value
                    suggestions.append(new_args)

        return suggestions

    def _generate_generic_parameter_suggestions(self, original_args: Dict[str, Any],
                                              tool_schema: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """生成通用参数建议"""
        suggestions = []

        # 常见参数名称映射
        param_mappings = {
            'count': ['limit', 'size', 'num', 'number', 'max'],
            'limit': ['count', 'size', 'num', 'number', 'max'],
            'query': ['q', 'search', 'keyword', 'term'],
            'platform': ['source', 'site', 'provider'],
        }

        for original_param, value in original_args.items():
            if original_param in param_mappings:
                for alternative_param in param_mappings[original_param]:
                    new_args = original_args.copy()
                    new_args.pop(original_param, None)
                    new_args[alternative_param] = value
                    suggestions.append(new_args)

        return suggestions

    def _generate_schema_based_suggestions(self, original_args: Dict[str, Any],
                                         tool_schema: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于工具schema生成参数建议"""
        suggestions = []

        try:
            # 从schema中提取参数信息
            if 'inputSchema' in tool_schema:
                schema = tool_schema['inputSchema']
                if 'properties' in schema:
                    required_params = schema.get('required', [])
                    properties = schema['properties']

                    # 生成只包含必需参数的建议
                    if required_params:
                        new_args = {}
                        for param in required_params:
                            if param in original_args:
                                new_args[param] = original_args[param]
                            elif param in properties:
                                # 尝试从原始参数中找到相似的参数
                                for orig_key, orig_value in original_args.items():
                                    if orig_key.lower() in param.lower() or param.lower() in orig_key.lower():
                                        new_args[param] = orig_value
                                        break

                        if new_args:
                            suggestions.append(new_args)

        except Exception as e:
            logger.warning(f"解析工具schema失败: {e}")

        return suggestions

    async def _get_tool_schema(self, server_config: Dict[str, Any], tool_name: str) -> Dict[str, Any]:
        """获取工具的schema信息"""
        try:
            connection_type = server_config.get("connection_type", "stdio")

            if connection_type == "http":
                tools_result = await self._get_http_tools(server_config)
                if tools_result.get("success"):
                    tools = tools_result.get("tools", [])
                    for tool in tools:
                        if tool.get("name") == tool_name:
                            return tool

            return {}
        except Exception as e:
            logger.warning(f"获取工具schema失败: {e}")
            return {}

    def _parse_result(self, result) -> Dict[str, Any]:
        """解析MCP调用结果"""
        try:
            if hasattr(result, 'content'):
                content = result.content
                if isinstance(content, list) and len(content) > 0:
                    # 提取文本内容
                    text_content = []
                    for item in content:
                        if hasattr(item, 'text'):
                            text_content.append(item.text)
                        else:
                            text_content.append(str(item))

                    return {
                        "success": True,
                        "content": "\n".join(text_content),
                        "raw_content": content
                    }
                else:
                    return {
                        "success": True,
                        "content": str(content),
                        "raw_content": content
                    }
            else:
                return {
                    "success": True,
                    "content": str(result),
                    "raw_content": result
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"解析结果失败: {str(e)}"
            }

    def get_available_tools(self, server_name: str) -> Dict[str, Any]:
        """获取服务的可用工具列表"""
        try:
            result = asyncio.run(self._async_get_tools(server_name))
            return result
        except Exception as e:
            return {
                "success": False,
                "error": f"获取工具列表失败: {str(e)}"
            }

    async def _async_get_tools(self, server_name: str) -> Dict[str, Any]:
        """异步获取工具列表"""
        try:
            server_config = self.manager.get_server_config(server_name)
            if not server_config:
                return {
                    "success": False,
                    "error": f"未找到服务 {server_name} 的配置"
                }

            connection_type = server_config.get("connection_type", "stdio")

            if connection_type == "http":
                return await self._get_http_tools(server_config)
            else:
                return await self._get_stdio_tools(server_name)

        except Exception as e:
            return {
                "success": False,
                "error": f"获取工具列表失败: {str(e)}"
            }

    async def _get_http_tools(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取HTTP服务的工具列表"""
        try:
            import mcp
            from mcp.client.streamable_http import streamablehttp_client

            # 构建连接URL
            config = server_config.get('config', {})
            config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
            api_key = server_config.get('api_key', '')
            server_name = server_config.get('server_name', '')

            url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"

            # 连接并获取工具列表
            async with streamablehttp_client(url) as (read_stream, write_stream, _):
                async with mcp.ClientSession(read_stream, write_stream) as session:
                    await session.initialize()

                    # 获取工具列表
                    tools_result = await session.list_tools()

                    tools = []
                    for tool in tools_result.tools:
                        tool_info = {
                            "name": tool.name,
                            "description": getattr(tool, 'description', ''),
                        }

                        # 添加输入schema
                        if hasattr(tool, 'inputSchema'):
                            tool_info["inputSchema"] = tool.inputSchema

                        tools.append(tool_info)

                    return {
                        "success": True,
                        "tools": tools
                    }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取HTTP工具列表失败: {str(e)}"
            }

    async def _get_stdio_tools(self, server_name: str) -> Dict[str, Any]:
        """获取STDIO服务的工具列表"""
        try:
            from .mcp_client import mcp_manager

            # 确保服务已连接
            if not await mcp_manager.connect_to_server(server_name):
                return {
                    "success": False,
                    "error": f"无法连接到STDIO服务器 {server_name}"
                }

            # 获取工具列表
            tools_result = await mcp_manager.get_available_tools(server_name)

            if tools_result is None:
                return {
                    "success": False,
                    "error": "获取STDIO工具列表返回空结果"
                }

            # 转换为标准格式
            tools = []
            for tool in tools_result:
                tool_info = {
                    "name": tool.name,
                    "description": getattr(tool, 'description', ''),
                }

                # 添加输入schema
                if hasattr(tool, 'inputSchema'):
                    tool_info["inputSchema"] = tool.inputSchema

                tools.append(tool_info)

            return {
                "success": True,
                "tools": tools
            }

        except Exception as e:
            logger.error(f"获取STDIO工具列表失败: {e}")
            return {
                "success": False,
                "error": f"获取STDIO工具列表失败: {str(e)}"
            }

class IntelligentMCPAgent:
    """智能MCP代理 - 自动选择服务和调整参数"""

    def __init__(self):
        self.universal_mcp = UniversalMCPInterface()

    def auto_call_mcp(self, user_intent: str, suggested_server: str = None,
                     suggested_tool: str = None, suggested_args: Dict[str, Any] = None) -> tuple[bool, str]:
        """
        智能MCP调用 - 根据用户意图自动选择服务和参数

        Args:
            user_intent: 用户意图描述
            suggested_server: 建议的服务名（可选）
            suggested_tool: 建议的工具名（可选）
            suggested_args: 建议的参数（可选）

        Returns:
            (是否成功, 结果内容或错误信息)
        """
        try:
            # 1. 如果有明确的建议，先尝试
            if suggested_server and suggested_tool:
                logger.info(f"尝试建议的服务: {suggested_server}.{suggested_tool}")
                success, result = self._try_suggested_call(suggested_server, suggested_tool, suggested_args or {})
                if success:
                    return True, result
                else:
                    logger.warning(f"建议的调用失败: {result}")

            # 2. 自动选择和尝试合适的服务
            return self._intelligent_service_selection(user_intent, suggested_args or {})

        except Exception as e:
            logger.error(f"智能MCP调用失败: {e}")
            return False, f"智能调用失败: {str(e)}"

    def _try_suggested_call(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> tuple[bool, str]:
        """尝试建议的调用"""
        return self.universal_mcp.use_mcp_tool(server_name, tool_name, arguments)

    def _intelligent_service_selection(self, user_intent: str, base_args: Dict[str, Any]) -> tuple[bool, str]:
        """智能服务选择和调用"""

        # 获取所有可用的服务
        available_services = self._get_available_services()

        # 根据用户意图匹配服务
        service_candidates = self._match_services_by_intent(user_intent, available_services)

        logger.info(f"找到 {len(service_candidates)} 个候选服务: {[s['name'] for s in service_candidates]}")

        # 尝试每个候选服务
        for candidate in service_candidates:
            server_name = candidate['name']
            tools = candidate.get('tools', [])

            logger.info(f"尝试服务: {server_name}")

            # 尝试该服务的每个工具
            for tool in tools:
                tool_name = tool.get('name')
                logger.info(f"  尝试工具: {tool_name}")

                # 生成适合该工具的参数
                adapted_args = self._adapt_arguments_for_tool(base_args, user_intent, tool)

                logger.info(f"  使用参数: {adapted_args}")

                success, result = self.universal_mcp.use_mcp_tool(server_name, tool_name, adapted_args)

                if success:
                    logger.info(f"✅ 成功调用: {server_name}.{tool_name}")
                    return True, result
                else:
                    logger.warning(f"  失败: {result}")

        return False, "未找到合适的MCP服务来处理此请求"

    def _get_available_services(self) -> List[Dict[str, Any]]:
        """获取所有可用的服务及其工具"""
        services = []

        try:
            all_servers = self.universal_mcp.manager.get_all_servers()

            for server_name, config in all_servers.items():
                if not config.get('enabled', False):
                    continue

                # 获取服务的工具列表
                tools_result = self.universal_mcp.get_available_tools(server_name)

                if tools_result.get('success'):
                    tools = tools_result.get('tools', [])
                    services.append({
                        'name': server_name,
                        'config': config,
                        'tools': tools
                    })
                    logger.info(f"服务 {server_name} 有 {len(tools)} 个工具")
                else:
                    logger.warning(f"无法获取服务 {server_name} 的工具列表")

        except Exception as e:
            logger.error(f"获取可用服务失败: {e}")

        return services

    def _match_services_by_intent(self, user_intent: str, services: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """根据用户意图匹配服务"""
        intent_lower = user_intent.lower()

        # 服务匹配规则
        service_patterns = {
            'hotnews': ['热榜', '热搜', '热点', '新闻', '微博', '知乎', '百度', 'b站', '抖音', '豆瓣', '虎扑'],
            'duckduckgo-remote': ['搜索', 'search', '查找', '查询'],
            'chinarailway': ['火车', '高铁', '铁路', '车票', '列车'],
        }

        matched_services = []

        # 优先匹配特定服务
        for service in services:
            service_name = service['name']
            if service_name in service_patterns:
                patterns = service_patterns[service_name]
                if any(pattern in intent_lower for pattern in patterns):
                    matched_services.append(service)
                    logger.info(f"服务 {service_name} 匹配用户意图")

        # 如果没有特定匹配，返回所有可用服务
        if not matched_services:
            matched_services = services
            logger.info("没有特定匹配，将尝试所有可用服务")

        return matched_services

    def _adapt_arguments_for_tool(self, base_args: Dict[str, Any], user_intent: str,
                                 tool_info: Dict[str, Any]) -> Dict[str, Any]:
        """为特定工具适配参数"""
        adapted_args = base_args.copy()
        tool_name = tool_info.get('name', '')
        intent_lower = user_intent.lower()

        # 根据工具类型和用户意图调整参数
        if 'hot' in tool_name.lower() or 'news' in tool_name.lower():
            # 热榜/新闻类工具
            adapted_args = self._adapt_for_news_tool(adapted_args, intent_lower)

        elif 'search' in tool_name.lower():
            # 搜索类工具
            adapted_args = self._adapt_for_search_tool(adapted_args, intent_lower)

        elif 'train' in tool_name.lower() or 'railway' in tool_name.lower():
            # 火车类工具
            adapted_args = self._adapt_for_railway_tool(adapted_args, intent_lower)

        return adapted_args

    def _adapt_for_news_tool(self, args: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """为新闻/热榜工具适配参数"""
        adapted = args.copy()

        # 平台映射
        platform_mappings = {
            '知乎': [1], '微博': [5], '百度': [3], 'b站': [4], '哔哩哔哩': [4],
            '抖音': [6], '豆瓣': [8], '虎扑': [7], '36氪': [2], 'it': [9]
        }

        # 从意图中提取平台
        detected_platforms = []
        for platform, source_ids in platform_mappings.items():
            if platform in intent:
                detected_platforms.extend(source_ids)

        if detected_platforms:
            adapted['sources'] = detected_platforms
        elif 'sources' not in adapted:
            # 默认使用知乎
            adapted['sources'] = [1]

        # 移除可能冲突的参数
        for key in ['platform', 'source', 'count']:
            adapted.pop(key, None)

        return adapted

    def _adapt_for_search_tool(self, args: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """为搜索工具适配参数"""
        adapted = args.copy()

        # 如果没有query，从意图中提取
        if 'query' not in adapted:
            # 提取搜索关键词
            if '微博' in intent:
                adapted['query'] = 'site:weibo.com 最新'
            elif '知乎' in intent:
                adapted['query'] = 'site:zhihu.com'
            else:
                # 提取关键词
                keywords = []
                for word in intent.split():
                    if word not in ['查询', '搜索', '最新', '条']:
                        keywords.append(word)
                adapted['query'] = ' '.join(keywords) if keywords else intent

        # 参数名映射
        if 'count' in adapted:
            adapted['limit'] = adapted.pop('count')

        return adapted

    def _adapt_for_railway_tool(self, args: Dict[str, Any], intent: str) -> Dict[str, Any]:
        """为火车工具适配参数"""
        adapted = args.copy()

        # 火车相关的参数适配逻辑
        # 这里可以根据具体的火车服务API来调整

        return adapted

# 创建全局实例
universal_mcp = UniversalMCPInterface()
intelligent_agent = IntelligentMCPAgent()
