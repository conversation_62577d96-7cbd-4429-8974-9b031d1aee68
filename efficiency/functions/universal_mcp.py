#!/usr/bin/env python3
"""
通用MCP工具调用接口
"""
import asyncio
import json
import base64
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

try:
    from .mcp_server import MCPServerManager
except ImportError:
    from mcp_server import MCPServerManager

# 设置日志
logger = logging.getLogger(__name__)

class UniversalMCPInterface:
    """通用MCP接口"""

    def __init__(self):
        self.manager = MCPServerManager()

    def use_mcp_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> tuple[bool, str]:
        """
        同步版本的MCP工具调用接口

        Args:
            server_name: MCP服务名称
            tool_name: 工具名称
            arguments: 工具参数

        Returns:
            (是否成功, 结果内容或错误信息)
        """
        try:
            # 添加调试日志
            logger.info(f"开始MCP工具调用: {server_name}.{tool_name}, 参数: {arguments}")

            # 运行异步调用
            result = asyncio.run(self._async_use_mcp_tool(server_name, tool_name, arguments))

            logger.info(f"MCP工具调用结果: {result}")

            if result["success"]:
                return True, result["content"]
            else:
                return False, result["error"]

        except Exception as e:
            logger.error(f"MCP工具调用失败: {server_name}.{tool_name}, 错误: {e}")
            return False, f"调用失败: {str(e)}"

    async def _async_use_mcp_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """异步版本的MCP工具调用"""
        try:
            # 获取服务配置
            server_config = self.manager.get_server_config(server_name)
            logger.info(f"获取服务配置: {server_name}, 配置: {server_config}")

            if not server_config:
                return {
                    "success": False,
                    "error": f"未找到服务 {server_name} 的配置"
                }

            enabled_status = server_config.get("enabled", False)
            logger.info(f"服务 {server_name} 启用状态: {enabled_status}")

            if not enabled_status:
                return {
                    "success": False,
                    "error": f"服务 {server_name} 未启用"
                }

            # 检查服务状态
            status = self.manager.get_server_status(server_name)
            logger.info(f"服务 {server_name} 运行状态: {status}")

            if status != 'running':
                return {
                    "success": False,
                    "error": f"服务 {server_name} 未运行，当前状态: {status}"
                }

            # 预处理工具名称和参数
            corrected_tool_name = self._correct_tool_name(server_name, tool_name)
            processed_arguments = self._preprocess_arguments(arguments)

            # 根据连接类型处理
            connection_type = server_config.get("connection_type", "stdio")

            if connection_type == "http":
                return await self._call_http_tool(server_config, corrected_tool_name, processed_arguments)
            else:
                return await self._call_stdio_tool(server_name, corrected_tool_name, processed_arguments)

        except Exception as e:
            logger.error(f"MCP工具调用失败: {server_name}.{tool_name}, 错误: {e}")
            return {
                "success": False,
                "error": f"调用失败: {str(e)}"
            }

    async def _call_http_tool(self, server_config: Dict[str, Any], tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用HTTP类型的MCP工具"""
        try:
            import mcp
            from mcp.client.streamable_http import streamablehttp_client

            # 预处理参数
            processed_args = self._preprocess_arguments(arguments)

            # 构建连接URL
            config = server_config.get('config', {})
            config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
            api_key = server_config.get('api_key', '')
            server_name = server_config.get('server_name', '')

            url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"

            # 连接并调用
            async with streamablehttp_client(url) as (read_stream, write_stream, _):
                async with mcp.ClientSession(read_stream, write_stream) as session:
                    await session.initialize()

                    # 调用工具
                    result = await session.call_tool(tool_name, processed_args)

                    # 解析结果
                    return self._parse_result(result)

        except ImportError:
            return {
                "success": False,
                "error": "MCP SDK未安装，请运行: pip install mcp"
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"HTTP调用失败: {str(e)}"
            }

    async def _call_stdio_tool(self, server_name: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用STDIO类型的MCP工具"""
        return {
            "success": False,
            "error": "STDIO类型的MCP调用暂未实现"
        }

    def _correct_tool_name(self, server_name: str, tool_name: str) -> str:
        """修正工具名称，处理常见的错误映射"""
        # 针对不同服务的工具名称映射
        tool_mappings = {
            'chinarailway': {
                'query_trains': 'search',
                'search_trains': 'search',
                'find_trains': 'search',
                'get_trains': 'search',
            },
            'files': {
                'list': 'list_directory',
                'ls': 'list_directory',
                'read': 'read_file',
                'cat': 'read_file',
                'write': 'write_file',
            }
        }

        # 获取服务特定的映射
        server_mappings = tool_mappings.get(server_name, {})

        # 返回映射后的工具名称，如果没有映射则返回原名称
        return server_mappings.get(tool_name, tool_name)

    def _preprocess_arguments(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """预处理参数，处理常见的格式转换"""
        processed = arguments.copy()

        # 处理常见的参数名映射
        param_mappings = {
            'from': 'fromCity',
            'to': 'toCity',
            '明天': 'tomorrow',
            '今天': 'today',
            '昨天': 'yesterday'
        }

        # 应用参数名映射
        for old_key, new_key in param_mappings.items():
            if old_key in processed:
                processed[new_key] = processed.pop(old_key)

        # 处理日期格式
        for key, value in processed.items():
            if isinstance(value, str):
                # 处理相对日期
                if value.lower() in ['today', '今天']:
                    processed[key] = datetime.now().strftime("%Y-%m-%d")
                elif value.lower() in ['tomorrow', '明天']:
                    processed[key] = (datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
                elif value.lower() in ['yesterday', '昨天']:
                    processed[key] = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

        return processed

    def _parse_result(self, result) -> Dict[str, Any]:
        """解析MCP调用结果"""
        try:
            if hasattr(result, 'content'):
                content = result.content
                if isinstance(content, list) and len(content) > 0:
                    # 提取文本内容
                    text_content = []
                    for item in content:
                        if hasattr(item, 'text'):
                            text_content.append(item.text)
                        else:
                            text_content.append(str(item))

                    return {
                        "success": True,
                        "content": "\n".join(text_content),
                        "raw_content": content
                    }
                else:
                    return {
                        "success": True,
                        "content": str(content),
                        "raw_content": content
                    }
            else:
                return {
                    "success": True,
                    "content": str(result),
                    "raw_content": result
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"解析结果失败: {str(e)}"
            }

    def get_available_tools(self, server_name: str) -> Dict[str, Any]:
        """获取服务的可用工具列表"""
        try:
            result = asyncio.run(self._async_get_tools(server_name))
            return result
        except Exception as e:
            return {
                "success": False,
                "error": f"获取工具列表失败: {str(e)}"
            }

    async def _async_get_tools(self, server_name: str) -> Dict[str, Any]:
        """异步获取工具列表"""
        try:
            server_config = self.manager.get_server_config(server_name)
            if not server_config:
                return {
                    "success": False,
                    "error": f"未找到服务 {server_name} 的配置"
                }

            connection_type = server_config.get("connection_type", "stdio")

            if connection_type == "http":
                return await self._get_http_tools(server_config)
            else:
                return {
                    "success": False,
                    "error": "STDIO类型的工具列表获取暂未实现"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取工具列表失败: {str(e)}"
            }

    async def _get_http_tools(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """获取HTTP服务的工具列表"""
        try:
            import mcp
            from mcp.client.streamable_http import streamablehttp_client

            # 构建连接URL
            config = server_config.get('config', {})
            config_b64 = base64.b64encode(json.dumps(config).encode()).decode()
            api_key = server_config.get('api_key', '')
            server_name = server_config.get('server_name', '')

            url = f"https://server.smithery.ai/{server_name}/mcp?config={config_b64}&api_key={api_key}"

            # 连接并获取工具列表
            async with streamablehttp_client(url) as (read_stream, write_stream, _):
                async with mcp.ClientSession(read_stream, write_stream) as session:
                    await session.initialize()

                    # 获取工具列表
                    tools_result = await session.list_tools()

                    tools = []
                    for tool in tools_result.tools:
                        tool_info = {
                            "name": tool.name,
                            "description": getattr(tool, 'description', ''),
                        }

                        # 添加输入schema
                        if hasattr(tool, 'inputSchema'):
                            tool_info["inputSchema"] = tool.inputSchema

                        tools.append(tool_info)

                    return {
                        "success": True,
                        "tools": tools
                    }

        except Exception as e:
            return {
                "success": False,
                "error": f"获取HTTP工具列表失败: {str(e)}"
            }

# 创建全局实例
universal_mcp = UniversalMCPInterface()
