#!/usr/bin/env python3
"""
测试STDIO类型的MCP服务
"""
import sys
import os
import logging
import json

# 添加项目路径
sys.path.append('/app')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_stdio_mcp_services():
    """测试STDIO类型的MCP服务"""
    try:
        from functions.universal_mcp import universal_mcp
        from functions.mcp_server import MCPServerManager
        
        print("=== 测试STDIO MCP服务 ===")
        
        # 1. 检查配置中的STDIO服务
        print("\n1. 检查STDIO服务配置:")
        all_servers = universal_mcp.manager.get_all_servers()
        
        stdio_servers = []
        for server_name, config in all_servers.items():
            connection_type = config.get("connection_type", "stdio")
            if connection_type == "stdio" or "command" in config:
                stdio_servers.append((server_name, config))
                print(f"  - {server_name}: {config.get('command', 'N/A')} {config.get('args', [])}")
        
        if not stdio_servers:
            print("  ❌ 没有找到STDIO类型的服务")
            return
        
        # 2. 启动STDIO服务
        print("\n2. 启动STDIO服务:")
        server_manager = MCPServerManager()
        
        for server_name, config in stdio_servers:
            if config.get("enabled", False):
                print(f"  启动服务: {server_name}")
                success = server_manager.start_server(server_name)
                print(f"  结果: {'✅ 成功' if success else '❌ 失败'}")
                
                # 检查服务状态
                status = server_manager.get_server_status(server_name)
                print(f"  状态: {status}")
            else:
                print(f"  跳过未启用的服务: {server_name}")
        
        # 3. 测试工具列表获取
        print("\n3. 测试工具列表获取:")
        for server_name, config in stdio_servers:
            if config.get("enabled", False):
                print(f"  获取 {server_name} 的工具列表:")
                
                tools_result = universal_mcp.get_available_tools(server_name)
                
                if tools_result.get("success"):
                    tools = tools_result.get("tools", [])
                    print(f"    ✅ 找到 {len(tools)} 个工具:")
                    for tool in tools[:3]:  # 只显示前3个
                        print(f"      - {tool.get('name')}: {tool.get('description', '')}")
                else:
                    print(f"    ❌ 获取失败: {tools_result.get('error')}")
        
        # 4. 测试工具调用
        print("\n4. 测试工具调用:")
        for server_name, config in stdio_servers:
            if config.get("enabled", False):
                print(f"  测试 {server_name} 的工具调用:")
                
                # 先获取工具列表
                tools_result = universal_mcp.get_available_tools(server_name)
                
                if tools_result.get("success"):
                    tools = tools_result.get("tools", [])
                    if tools:
                        # 测试第一个工具
                        first_tool = tools[0]
                        tool_name = first_tool.get("name")
                        
                        print(f"    测试工具: {tool_name}")
                        
                        # 构建测试参数
                        test_args = {}
                        if "files" in server_name.lower():
                            test_args = {"path": "."}
                        elif "playwright" in server_name.lower():
                            test_args = {"url": "https://example.com"}
                        
                        success, result = universal_mcp.use_mcp_tool(server_name, tool_name, test_args)
                        
                        if success:
                            print(f"    ✅ 调用成功!")
                            result_preview = result[:200] + "..." if len(result) > 200 else result
                            print(f"    结果预览: {result_preview}")
                        else:
                            print(f"    ❌ 调用失败: {result}")
                    else:
                        print(f"    ⚠️ 没有可用工具")
                else:
                    print(f"    ❌ 无法获取工具列表")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_enable_stdio_service():
    """启用一个STDIO服务进行测试"""
    try:
        print("\n=== 启用files服务进行测试 ===")
        
        # 读取配置
        config_path = "/app/config/mcp_config.json"
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 启用files服务
        if "files" in config["mcpServers"]:
            config["mcpServers"]["files"]["enabled"] = True
            
            # 写回配置
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            print("✅ 已启用files服务")
            return True
        else:
            print("❌ 未找到files服务配置")
            return False
            
    except Exception as e:
        print(f"❌ 启用服务失败: {e}")
        return False

if __name__ == "__main__":
    # 首先启用一个STDIO服务
    if test_enable_stdio_service():
        # 然后测试STDIO功能
        test_stdio_mcp_services()
    else:
        print("无法启用STDIO服务，跳过测试")
