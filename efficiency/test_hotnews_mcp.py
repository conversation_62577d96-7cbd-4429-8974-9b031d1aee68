#!/usr/bin/env python3
"""
HotNews MCP服务测试脚本
"""
import sys
import os
import logging
import json

# 添加项目路径
sys.path.append('/app')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_hotnews_service():
    """测试HotNews MCP服务"""
    try:
        from functions.universal_mcp import universal_mcp
        
        print("=== HotNews MCP服务测试 ===")
        
        # 1. 检查服务配置
        print("\n1. 检查服务配置:")
        server_config = universal_mcp.manager.get_server_config("hotnews")
        if server_config:
            print(f"✅ 服务配置: {json.dumps(server_config, indent=2, ensure_ascii=False)}")
        else:
            print("❌ 未找到hotnews服务配置")
            return
        
        # 2. 检查服务状态
        print("\n2. 检查服务状态:")
        status = universal_mcp.manager.get_server_status("hotnews")
        print(f"服务状态: {status}")
        
        if status != 'running':
            print(f"⚠️ 服务状态不是running: {status}")
        
        # 3. 获取可用工具列表
        print("\n3. 获取可用工具:")
        tools_result = universal_mcp.get_available_tools("hotnews")
        
        if tools_result.get("success"):
            tools = tools_result.get("tools", [])
            print(f"✅ 找到 {len(tools)} 个工具:")
            for tool in tools:
                print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
        else:
            print(f"❌ 获取工具列表失败: {tools_result.get('error', 'Unknown error')}")
            return
        
        # 4. 测试工具调用（如果有工具的话）
        if tools:
            print("\n4. 测试工具调用:")
            first_tool = tools[0]
            tool_name = first_tool.get('name')
            
            print(f"测试工具: {tool_name}")
            
            # 根据工具名称构建测试参数
            test_args = {}
            
            # 常见的新闻相关参数
            if 'news' in tool_name.lower() or 'hot' in tool_name.lower():
                test_args = {"limit": 5}
            elif 'search' in tool_name.lower():
                test_args = {"query": "technology", "limit": 5}
            elif 'get' in tool_name.lower():
                test_args = {"count": 5}
            
            print(f"测试参数: {json.dumps(test_args, ensure_ascii=False)}")
            
            # 调用工具
            success, result = universal_mcp.use_mcp_tool("hotnews", tool_name, test_args)
            
            if success:
                print("✅ 工具调用成功!")
                print("结果预览:")
                result_preview = result[:500] + "..." if len(result) > 500 else result
                print(result_preview)
            else:
                print(f"❌ 工具调用失败: {result}")
        else:
            print("\n4. 没有可用工具进行测试")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_xml_call():
    """测试XML格式的MCP调用"""
    try:
        from pages.components.chat import execute_action
        
        print("\n=== 测试XML格式调用 ===")
        
        # 构建XML调用（先用一个通用的工具名）
        xml_call = '''<use_mcp_tool>
<server_name>hotnews</server_name>
<tool_name>get_hot_news</tool_name>
<arguments>{"limit": 5}</arguments>
</use_mcp_tool>'''
        
        print("XML调用:")
        print(xml_call)
        
        print("\n执行结果:")
        is_end, result = execute_action(xml_call)
        
        if 'is_success: True' in result:
            print("✅ XML调用成功")
        elif 'is_success: False' in result:
            print("❌ XML调用失败")
        else:
            print("⚠️ 未知状态")
        
        print(f"结果: {result[:300]}...")
        
    except Exception as e:
        print(f"❌ XML调用测试失败: {e}")

if __name__ == "__main__":
    test_hotnews_service()
    test_xml_call()
