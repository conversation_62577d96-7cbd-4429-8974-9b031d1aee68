#!/usr/bin/env python3
"""
MCP调试测试脚本
"""
import sys
import os
import logging

# 添加项目路径
sys.path.append('/app')
sys.path.append('/app/efficiency')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_mcp_direct():
    """直接测试MCP工具调用"""
    try:
        from functions.universal_mcp import universal_mcp
        
        print("=== 开始MCP调试测试 ===")
        
        # 测试服务配置获取
        print("\n1. 测试服务配置获取:")
        server_config = universal_mcp.manager.get_server_config("duckduckgo-remote")
        print(f"duckduckgo-remote 配置: {server_config}")
        
        # 测试服务状态
        print("\n2. 测试服务状态:")
        status = universal_mcp.manager.get_server_status("duckduckgo-remote")
        print(f"duckduckgo-remote 状态: {status}")
        
        # 测试所有服务
        print("\n3. 测试所有服务:")
        all_servers = universal_mcp.manager.get_all_servers()
        for name, config in all_servers.items():
            enabled = config.get("enabled", False)
            server_status = universal_mcp.manager.get_server_status(name)
            print(f"  {name}: enabled={enabled}, status={server_status}")
        
        # 测试MCP工具调用
        print("\n4. 测试MCP工具调用:")
        success, result = universal_mcp.use_mcp_tool(
            "duckduckgo-remote", 
            "search", 
            {"query": "python教程", "limit": 5}
        )
        print(f"调用结果: success={success}")
        print(f"结果内容: {result}")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_function_call():
    """测试function call机制"""
    try:
        from pages.components.chat import abilities
        
        print("\n=== 测试Function Call机制 ===")
        
        # 检查use_mcp_tool是否在abilities中
        print(f"use_mcp_tool 在 abilities 中: {'use_mcp_tool' in abilities}")
        
        if 'use_mcp_tool' in abilities:
            func = abilities['use_mcp_tool']
            print(f"use_mcp_tool 函数: {func}")
            
            # 直接调用
            print("\n测试直接调用:")
            success, result = func(
                server_name="duckduckgo-remote",
                tool_name="search", 
                arguments={"query": "python教程", "limit": 5}
            )
            print(f"直接调用结果: success={success}")
            print(f"结果内容: {result}")
        
    except Exception as e:
        print(f"Function call测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mcp_direct()
    test_function_call()
